import { createRouter, createWebHistory } from 'vue-router'


const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',  //path用来设置路由地址
      name: 'index',  //name设置路由名称、名字不是必须的、可配可不配，推荐配
      component: () => import('../views/Index.vue') //设置路由对应的视图页面
    },

    {
      path: '/home',
      name: 'home',
      component: () => import('../views/Home.vue')
    },
    
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/Login.vue')
    }
  ],
})

export default router
