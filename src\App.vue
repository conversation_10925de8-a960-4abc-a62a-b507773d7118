
<template>

  <router-view/>

  <!-- App.vue是整个Vue项目展示页面内容的地方

    如果 希望 路由 对应的 视图 内容 显示到 浏览器中， 必须在 App.vue 中 提供 渲染出

    router-view标签作用就是将路由对应的视图 渲染的出口位置 -->

</template>

<script>
import Login from './views/Login.vue';
import Index from './views/Index.vue';

export default {
  components: {
    Login,
    Index,
  },
  computed: {
    showIndex() {
      return this.$route.path == '/login';  // 非登录页才显示
    },
  },
}
</script>

<style>
/* 全局样式重置 - 移除 scoped 让样式应用到全局 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-x: hidden;
}

#app {
  margin: 0;
  padding: 0;
  height: 100vh;
}

/* 重置 Element Plus 默认间距 */
.el-container {
  margin: 0;
  padding: 0;
}

.el-header {
  margin: 0;
  padding: 0;
}

.el-aside {
  margin: 0;
  padding: 0;
}

.el-main {
  margin: 0;
  padding: 0;
}
</style>
