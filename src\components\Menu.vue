<template>

  <el-row class="tac">
    <el-col >
      <el-menu
        default-active="2"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
      >
        <Input />

        <div class="menu-item">
            <el-menu-item index="1">
                <el-icon style="color: red;"><House /></el-icon>
                <span>主页</span>
            </el-menu-item>

            <el-menu-item index="2">  <!-- 加 disabled 禁用 -->
                <el-icon style="color: red;"><Headset /></el-icon>
                <span>音乐馆</span>
            </el-menu-item>
        </div>

      </el-menu>
    </el-col>
  </el-row>
</template>


<script lang="ts" setup>
import {
  Headset,
  House,
} from '@element-plus/icons-vue'
import Input from './Input.vue'


const handleOpen = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
const handleClose = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
</script>

<style scoped>


.menu-item {
    display: flex;
    flex-direction: column;
    color: #000;  /* 字体颜色 */
    padding-top: 20px;
    margin: auto;
    width: 240px;
    align-items: center;  /* 水平居中 */
    justify-content: center;  /* 垂直居中 */
}

.el-menu-item {
  height: 35px !important;
  width: 100%;
  font-size: 15px;
  padding-left: 3px !important;
  border-radius: 10px;
  display: flex;
  flex-direction: row;  /* 图标和文字水平排列 */
  align-items: center;  /* 垂直居中 */
  justify-content: flex-start;  /* 左对齐 */
  text-align: left;
}

.el-menu-vertical-demo {
  height: 100vh;
  background-color: #F9F9F9;
}

/* Element Plus 菜单项样式修复 */
:deep(.el-menu-item) {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  padding: 0 10px !important;
}

/* 图标样式 */
:deep(.el-menu-item .el-icon) {
  margin-right: 8px !important;
  font-size: 16px !important;
}

/* 文字样式 */
:deep(.el-menu-item span) {
  font-size: 15px !important;
}

/* 选中项背景色 */
:deep(.el-menu-item.is-active) {
  background-color: #E6E6E7 !important;
  color: #000;
  font-weight: bold;
}

/* 鼠标悬停项背景色 */
:deep(.el-menu-item:hover) {
    background-color: #E6E6E7 !important;
}

</style>
